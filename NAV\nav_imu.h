/***********************************************************************************
nav imu module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_IMU_H__
#define __NAV_IMU_H__
#include "nav_type.h"


#define   D2R                   (1.7453292519943295769236907684886e-2)    //度转弧度(pi/180.0)，无量纲
#define   G                     (9.78815815671269)                          //标定时采用的重力加速度当量，单位：m/s2
/*****************************************************************/
typedef double ftype;
typedef double ANGRATE;              //ANGRATE
typedef double ACCELER;              //ACCELER

typedef struct
{
    ftype x;
    ftype y;
    ftype z;

}V3_ST;//3
		
/*****************************************************************/

//函数声明
extern void Get_Param_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p); //输入数据获取
extern void Get_IMU_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p); //传感器数据获取
extern void Load_Calib_Parms(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p);   
extern unsigned char nav_calib(double *original,_calib_t* pcalib,double *standard);
extern void Load_Standard_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p);
extern void savebuff();

#endif

